//
//  IMYMainSettingVC.m
//  ZZIMYMain
//
//  Created by ljh on 2023/6/25.
//

#import "IMYMainSettingVC.h"
#import "IMYMainSettingCell.h"
#import "IMYMeGlobalMacros.h"
#import "IMYMeLoginManager.h"
#import "SYAutoUploadData.h"
#import "SYPublicFun.h"
#import "SYUserHelper+Method.h"
#import "SYAllUseVC.h"
#import "SYPushSettingVC.h"
#import "SYAuthorizationViewController.h"
#import "SYAboutViewCtl.h"

#import <IMYMe/SYUserHelper.h>
#import <IMYBaseKit/IMYActionSheetV2.h>
#import <IMYBaseKit/IMYCoolShareSheet.h>
#import <IMYBaseKit/IMYRM80AttributedLabelURL.h>
#import <BBJBabyHome/BBJUploader.h>
#import <BBJBabyHome/BBJBabyCacheManager.h>
#import <IMYMSG/IMYMsgNotifyManager.h>
#import <IMYRecord/IMYRecord.h>
#import <IMYYQBasicServices/IMYToolsFMCountDataManager.h>
#import <IMYTools/IMYFMCountSuspensionView.h>

@interface IMYMainSettingVC () <UITableViewDelegate, UITableViewDataSource, IMYRM80AttributedLabelDelegate>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSArray<NSArray *> *sections;

@property (nonatomic, assign) NSTimeInterval logoutBeginTime;
@property (nonatomic, strong) IMYDayRecordUpload *syncmlRecordUpload; //上传记录

@end

@implementation IMYMainSettingVC

- (void)viewDidLoad {
    [super viewDidLoad];
    self.title = @"设置";
    [self setupData];
    
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
    
    [self setupTableFooterView];
    
    @weakify(self);
    [[[IMYPublicAppHelper shareAppHelper].useridChangedSignal takeUntil:self.rac_willDeallocSignal] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        [self setupTableFooterView];
    }];
    
    RACSignal *appIconSettingChange = [[NSNotificationCenter defaultCenter] rac_addObserverForName:@"K_Noti_hasClickAppIconSettingChange" object:nil];
    RACSignal *rightSig = [IMYRightsSDK sharedInstance].loadedSignal;
    RACSignal *bigfontSig = [IMYAppBaseStyle sharedInstance].onChangedSignal;
    [[[RACSignal merge:@[appIconSettingChange, rightSig, bigfontSig]].deliverOnMainThread takeUntil:self.rac_willDeallocSignal] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        [self setupData];
        [self.tableView reloadData];
    }];
}

- (void)setupData {
    NSMutableArray *sections = [NSMutableArray array];
    
    {
        NSMutableArray *datas = [NSMutableArray array];
        [sections addObject:datas];
        
        {
            IMYMainSettingDataModel *model = [IMYMainSettingDataModel new];
            model.type = IMYMainSettingDataTypeSafely;
            model.title = @"账号与安全";
            [datas addObject:model];
        }
        {
            IMYMainSettingDataModel *model = [IMYMainSettingDataModel new];
            model.type = IMYMainSettingDataTypeFeedback;
            model.title = @"帮助与反馈";
            [datas addObject:model];
        }
    }
    
    {
        NSMutableArray *datas = [NSMutableArray array];
        
        {
            // 经期-全展示
            BOOL showYoungEntrance = NO;
            if (IMYPublicAppHelper.shareAppHelper.userMode == IMYVKUserModeNormal) {
                showYoungEntrance = YES;
            }
            if (showYoungEntrance) {
                IMYMainSettingDataModel *model = [IMYMainSettingDataModel new];
                model.type = IMYMainSettingDataTypeYoung;
                model.title = @"青少年模式";
                if ([IMYPublicAppHelper shareAppHelper].useYoungMode) {
                    model.subtitle = IMYString(@"已开启");
                }
                [datas addObject:model];
            }
        }
        {
            /// 前提：已登录
            /// 经期身份、备孕身份、育儿身份：用户存在关注的宝宝（已出生&胎宝宝）
            /// 亲友身份：均展示，并展示“已开启”标识
            BOOL condition = [SYUserHelper sharedHelper].isShowQyForSetting;
            if (condition && [IMYPublicAppHelper shareAppHelper].hasLogin) {
                IMYMainSettingDataModel *model = [IMYMainSettingDataModel new];
                model.type = IMYMainSettingDataTypeQinYouMode;
                model.title = IMYString(@"亲友模式");
                if ([IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModeQinYou) {
                    model.subtitle = IMYString(@"已开启");
                }
                [datas addObject:model];
            }
        }
        {
            /// 大字体设置-亲友模式均展示
            /// 前提: 亲友模式
            BOOL condition = [IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModeQinYou;
            if (condition && [IMYPublicAppHelper shareAppHelper].hasLogin) {
                IMYMainSettingDataModel *model = [IMYMainSettingDataModel new];
                model.type = IMYMainSettingDataTypeBigFontMode;
                model.title = IMYString(@"大字体");
                [[IMYAppBaseStyle sharedInstance] registAutoViewer:self];
                if([IMYAppBaseStyle sharedInstance].currentAppStyle == IMYAppStyleTypeBigFont) {
                    model.subtitle = IMYString(@"已开启");
                } else {
                    model.subtitle = IMYString(@"已关闭");
                }
                [datas addObject:model];
            }
        }
        
        {
            // 青少年模式下 无法切换极简模式（因为无会员权益），不展示极简
            BOOL useYoungMode = [IMYPublicAppHelper shareAppHelper].useYoungMode;
            if ([IMYRecordABTestManager isShowJiJianModeInAppSetting] && !useYoungMode) {
                IMYMainSettingDataModel *model = [IMYMainSettingDataModel new];
                model.type = IMYMainSettingDataTypeSimpleMode;
                model.title = @"极简模式";
                model.iconName = @"vip_simplified_goto_icon";
                [datas addObject:model];
            }
        }
        if (datas.count > 0) {
            [sections addObject:datas];
        }
    }
    
    {
        NSMutableArray *datas = [NSMutableArray array];
        
        {
            if ([SYPublicFun needShowAppIconSetting]) {
                IMYMainSettingDataModel *model = [IMYMainSettingDataModel new];
                model.type = IMYMainSettingDataTypeAppIconSetting;
                model.title = @"切换图标";
                if ([SYPublicFun needShowAppIconRedot]) {
                    model.iconName = @"all_setting_icon_new";
                } else {
                    model.iconName = @"vip_simplified_goto_icon";
                }
                [datas addObject:model];
            }
        }
        
        if (datas.count > 0) {
            [sections addObject:datas];
        }
    }
    
    {
        NSMutableArray *datas = [NSMutableArray array];
        [sections addObject:datas];
        
        {
            IMYMainSettingDataModel *model = [IMYMainSettingDataModel new];
            model.type = IMYMainSettingDataTypeMessageNotify;
            model.title = @"消息通知";
            [datas addObject:model];
        }
        {
            IMYMainSettingDataModel *model = [IMYMainSettingDataModel new];
            model.type = IMYMainSettingDataTypeExternalDevice;
            model.title = @"智能设备";
            [datas addObject:model];
        }
        {
            IMYMainSettingDataModel *model = [IMYMainSettingDataModel new];
            model.type = IMYMainSettingDataTypePermission;
            model.title = @"个人信息与权限";
            [datas addObject:model];
        }
        {
            IMYMainSettingDataModel *model = [IMYMainSettingDataModel new];
            model.type = IMYMainSettingDataTypeCommon;
            model.title = @"通用";
            [datas addObject:model];
        }
    }
    
    {
        NSMutableArray *datas = [NSMutableArray array];
        [sections addObject:datas];
        
        {
            BOOL isCommunityContentSettingOn = [[IMYConfigsCenter sharedInstance] boolForKeyPath:@"meetyou_app_setting.setting_items.community_content_setting"];
            if (isCommunityContentSettingOn) {
                IMYMainSettingDataModel *model = [IMYMainSettingDataModel new];
                model.type = IMYMainSettingDataTypeCommunityContentSetting;
                model.title = @"社区内容偏好调节";
                [datas addObject:model];
            }
        }
        {
            IMYMainSettingDataModel *model = [IMYMainSettingDataModel new];
            model.type = IMYMainSettingDataTypeCommunityConvention;
            model.title = @"社区公约";
            [datas addObject:model];
        }
    }
    
    {
        NSMutableArray *datas = [NSMutableArray array];
        [sections addObject:datas];
        
        {
            IMYMainSettingDataModel *model = [IMYMainSettingDataModel new];
            model.type = IMYMainSettingDataTypeAbout;
            model.title = @"关于美柚";
            [datas addObject:model];
        }
        {
            IMYMainSettingDataModel *model = [IMYMainSettingDataModel new];
            model.type = IMYMainSettingDataTypeShareApp;
            model.title = @"分享美柚";
            [datas addObject:model];
        }
    }
    
    self.sections = sections;
}

#pragma mark - UITableView

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return self.sections.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    NSArray *datas = self.sections[section];
    return datas.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    IMYMainSettingCell *cell = [tableView dequeueReusableCellWithIdentifier:@"cell" forIndexPath:indexPath];
    
    NSArray *datas = self.sections[indexPath.section];
    
    IMYMainSettingDataModel *model = datas[indexPath.row];
    cell.model = model;
    
    cell.lineView.hidden = YES;
    if (datas.count > 1) {
        if (indexPath.row == 0) {
            [cell.edgeBoxView imy_drawTopCornerRadius:12];
            cell.lineView.hidden = NO;
        } else if (indexPath.row == datas.count - 1) {
            [cell.edgeBoxView imy_drawBottomCornerRadius:12];
        } else {
            [cell.edgeBoxView imy_drawBottomCornerRadius:0];
            cell.lineView.hidden = NO;
        }
    } else {
        [cell.edgeBoxView imy_drawAllCornerRadius:12];
    }
    
    cell.imyut_eventInfo.showRadius = 1.0f;
    cell.imyut_eventInfo.eventName = [NSString stringWithFormat:@"IMYMainSettingCell_%ld", model.type];
    @weakify(self);
    cell.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *LKDBQueryParams) {
        @strongify(self);
        if (model.type == IMYMainSettingDataTypeQinYouMode) {
            /// yy_wtabszy_qyms：孕育_我tab设置页_亲友模式 上报【曝光】
            [IMYGAEventHelper postWithPath:@"event" params:@{@"event": @"yy_wtabszy_qyms", @"action": @(1)} headers:nil completed:nil];
        } else if (model.type == IMYMainSettingDataTypeAppIconSetting) {
            [IMYGAEventHelper postWithPath:@"event" params:@{@"event": @"sz_qhtb", @"action": @(1)} headers:nil completed:nil];
        }
    };
    
    return cell;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return 8;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    NSArray *datas = self.sections[indexPath.section];
    IMYMainSettingDataModel *model = datas[indexPath.row];
    
    if (![IMYPublicAppHelper shareAppHelper].hasLogin) {
        // 未登录情况，下列类型 需要先登录
        switch (model.type) {
            case IMYMainSettingDataTypeSafely: {
                [[IMYURIManager shareURIManager] runActionWithString:@"login"];
                return;
            } break;
            default:
                break;
        }
    }
    
    switch (model.type) {
        case IMYMainSettingDataTypeSafely: {
            // 账号安全
            IMYAccountSecureVC *vc = [IMYAccountSecureVC new];
            [self imy_push:vc];
        } break;
        case IMYMainSettingDataTypeFeedback: {
            // 意见反馈
            NSString *helperURL = [NSString stringWithFormat:@"%@/help/home.html", nodejs_user_seeyouyima_com];
            [[IMYURIManager shareURIManager] runActionWithPath:@"web/pure" params:@{@"url": helperURL} info:nil];
        } break;
        case IMYMainSettingDataTypeYoung: {
            // 【青少年入口】24：我的-设置-青少年模式入口
            [[IMYURIManager sharedInstance] runActionWithPath:@"youthmode/detail_page" params:@{@"entrance": @(24)} info:nil];
            
            // 埋点
            NSDictionary *params = @{
                @"event": @"qsn_rk",
                @"action": @(2),
                @"entrance": @(24)
            };
            [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
        } break;
        case IMYMainSettingDataTypeQinYouMode: {
            // 亲友模式
            if (IMYPublicAppHelper.shareAppHelper.useYoungMode) {
                [UIView imy_showTextHUD:@"你正在青少年模式中，无法切换亲友模式"];
                return;
            }
            [[IMYURIManager sharedInstance] runActionWithString:@"setting/qinyou_mode"];
        } break;
        case IMYMainSettingDataTypeBigFontMode:{
            // 大字体模式
            [[IMYURIManager sharedInstance] runActionWithString:@"setting/bigfont_mode"];
        } break;
        case IMYMainSettingDataTypeSimpleMode: {
            // 极简模式
            [[IMYURIManager shareURIManager] runActionWithString:@"my/simpleModeSwitch"];
        } break;
        case IMYMainSettingDataTypeMessageNotify: {
            // 消息通知
            SYPushSettingVC *vc = [SYPushSettingVC new];
            [self imy_push:vc];
        } break;
        case IMYMainSettingDataTypeExternalDevice: {
            // 智能设备
            [[IMYURIManager shareURIManager] runActionWithString:@"my/intellect/devices"];
        } break;
        case IMYMainSettingDataTypePermission: {
            // 个人信息与权限
            SYAuthorizationViewController *vc = [SYAuthorizationViewController new];
            [self imy_push:vc];
        } break;
        case IMYMainSettingDataTypeCommon: {
            // 通用
            SYAllUseVC *vc = [SYAllUseVC new];
            [self imy_push:vc];
        } break;
        case IMYMainSettingDataTypeCommunityContentSetting: {
            // 跳转社区内容偏好调节页面
            [[IMYURIManager sharedInstance] runActionWithString:@"circles/user_preference"];
        } break;
        case IMYMainSettingDataTypeCommunityConvention: {
            // 社区公约
            [[IMYURIManager shareURIManager] runActionWithPath:@"web" params:@{@"url":@"https://nodejs-user.seeyouyima.com/users/rules.html"} info:nil];
        } break;
        case IMYMainSettingDataTypeAbout: {
            // 关于
            SYAboutViewCtl *viewCtl = [SYAboutViewCtl new];
            [self imy_push:viewCtl];
        } break;
        case IMYMainSettingDataTypeShareApp: {
            // 分享
            [self showShareApplictionSheet];
        } break;
        case IMYMainSettingDataTypeAppIconSetting: {
            // 切换应用图标
            [[IMYURIManager sharedInstance] runActionWithPath:@"desktop/icon/setting" 
                                                       params:@{@"sceneKey": @"appicon"}
                                                         info:nil];
            [SYPublicFun markClickAppIconSetting:YES];
            [IMYGAEventHelper postWithPath:@"event" params:@{@"event": @"sz_qhtb", @"action": @(2)} headers:nil completed:nil];
        } break;
        default:
            break;
    }
    
}


#pragma mark - UI

- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:self.view.bounds];
        _tableView.dataSource = self;
        _tableView.delegate = self;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.rowHeight = UITableViewAutomaticDimension;
        _tableView.estimatedRowHeight = 48;
        _tableView.backgroundColor = [UIColor clearColor];
        [_tableView registerClass:IMYMainSettingCell.class forCellReuseIdentifier:@"cell"];
    }
    return _tableView;
}

- (void)setupTableFooterView {
    UIView *footerView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 8 + 48 + 8 + 17 + 8 + SCREEN_TABBAR_SAFEBOTTOM_MARGIN)];
    UIButton *loginButton = [[UIButton alloc] initWithFrame:CGRectMake(12, 8, SCREEN_WIDTH - 24, 48)];
    [loginButton addTarget:self action:@selector(onLoginButtonPressed:) forControlEvents:UIControlEventTouchUpInside];
    loginButton.titleLabel.font = [UIFont systemFontOfSize:17 weight:UIFontWeightRegular];
    NSString *title = [IMYPublicAppHelper shareAppHelper].hasLogin ? IMYString(@"退出登录") : IMYString(@"登录");
    [loginButton imy_setTitle:title];
    [loginButton imy_setTitleColor:kCK_Red_E];
    [loginButton imy_drawAllCornerRadius:12];
    [loginButton imy_addThemeChangedBlock:^(UIButton *weakObject) {
        [weakObject setBackgroundImage:[UIImage imy_imageFromColor:IMY_COLOR_KEY(kCK_White_AN) andSize:CGSizeMake(10, 10)] forState:UIControlStateNormal];
        [weakObject setBackgroundImage:[UIImage imy_imageFromColor:IMY_COLOR_KEY(kCK_Black_H) andSize:CGSizeMake(10, 10)] forState:UIControlStateHighlighted];
    }];
    [footerView addSubview:loginButton];
    
    NSString *infolistString = @"《个人信息收集清单》";
    NSString *thirdshareString = @"《第三方信息共享清单》";
    NSString *content3 = @"《隐私政策》";
    NSString *contentString = [NSString stringWithFormat:@"%@ %@ %@", infolistString, thirdshareString, content3];
    IMYRM80AttributedLabel *lawLabel = [[IMYRM80AttributedLabel alloc] initWithFrame:CGRectZero];
    lawLabel.underLineForLink = NO;
    lawLabel.font = [UIFont systemFontOfSize:12 weight:UIFontWeightRegular];
    [lawLabel imy_addThemeChangedBlock:^(IMYRM80AttributedLabel *weakObject) {
        weakObject.textColor = IMY_COLOR_KEY(kCK_Colour_A);
        weakObject.linkColor = IMY_COLOR_KEY(kCK_Colour_A);
    }];
    lawLabel.numberOfLines = 0;
    lawLabel.lineSpacing = 6;
    lawLabel.drawCenter = YES;
    lawLabel.delegate = self;
    [lawLabel setTextAlignment:kCTTextAlignmentCenter];
    [lawLabel setText:contentString];
    [lawLabel addCustomLink:infolistString forRange:[contentString rangeOfString:infolistString]];
    [lawLabel addCustomLink:thirdshareString forRange:[contentString rangeOfString:thirdshareString]];
    [lawLabel addCustomLink:content3 forRange:[contentString rangeOfString:content3]];
    [footerView addSubview:lawLabel];
    
    [lawLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(loginButton.mas_bottom).offset(8);
        make.centerX.mas_equalTo(footerView.mas_centerX).offset(0);
    }];
    
    self.tableView.tableFooterView = footerView;
}

#pragma mark - Login

- (BOOL)needShowFMCountDialog:(void (^)(void)) sureBlock {
    
    IMYToolsFMCountModel * model = [[IMYToolsFMCountDataManager sharedManager] getCountingRecord];
    if (model && !model.isEned) {
        IMYActionMessageBox *box = [IMYActionMessageBox showBoxWithTitle:@"" message:@"退出将结束并清空本次数胎动记录，确定要退出吗？" style:IMYMessageBoxStyleFlat isShowCloseButton:NO textAlignment:NSTextAlignmentCenter cancelButtonTitle:@"退出" otherButtonTitle:@"不退出" action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
            if (sender == messageBox.rightButton) {
                
            } else if (sender == messageBox.leftButton) {
                [[IMYToolsFMCountDataManager sharedManager] removeRecord:model];
                [[IMYFMCountSuspensionView sharedInstance] dismiss];
                sureBlock();
            }
            
            [messageBox dismiss];
        }];
        return YES;
    }
    
    return NO;
}

- (void)onLoginButtonPressed:(id)sender {
    if (![IMYPublicAppHelper shareAppHelper].hasLogin) {
        [IMYEventHelper event:@"wdzl-dl"];
        [[IMYURIManager shareURIManager] runActionWithPath:@"login" params:@{
            @"no_login_toast" : @YES,
            @"finishedBlock" : ^(UIViewController *loginVC){
            // 设置页面的登录按钮触发的登录，都回到首页
            [loginVC dismissViewControllerAnimated:YES completion:nil];
            UITabBarController *rootVC = [UIApplication sharedApplication].delegate.window.rootViewController;
            if ([rootVC isKindOfClass:UITabBarController.class]) {
                [[rootVC.selectedViewController imy_navigationController] popToRootViewControllerAnimated:NO];
                rootVC.selectedIndex = 0;
            }
        },
        } info:nil];
        return;
    }
    
    if ([IMYPublicAppHelper shareAppHelper].useYoungMode) {
        [UIView imy_showTextHUD:@"你正在青少年模式中，无法退出登录"];
        
        // 曝光
        NSDictionary *params = @{
            @"event": @"sz_qsntc",
            @"action": @(1)
        };
        [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
        
        return;
    }
    @weakify(self)
    if ([self needShowFMCountDialog:^{
        @strongify(self)
        [self isConfirmedAndLoginout];
    }]) {
        return;
    }
    [self loginOut];
}

- (void)loginOut {
    NSString *title = IMYString(@"确定要退出登录吗？");
    NSString *message = IMYString(@"你的数据随账号保存");
    NSUInteger picCount = [BBJUploader sharedInstance].publishModels.count;
    if (picCount > 0) {
        message = [NSString stringWithFormat:@"宝宝记%ld条记录正在保存中", picCount];
    }
    
    IMYActionSheetV2 *sheet = [[IMYActionSheetV2 alloc] initWithTitles:@[title, message] cancelButton:@"取消" otherButtons:@[@"退出登录"]];
    @weakify(self, sheet);
    sheet.onActionBlock = ^(NSInteger index) {
        @strongify(self, sheet);
        if (index == 1) {
            if (![IMYNetState networkEnable]) {
                // 无网络，继续保持sheet弹起
                [UIWindow imy_showTextHUD:IMYString(@"网络不见了，请检查网络")];
            } else {
                [self isConfirmedAndLoginout];
                [sheet dismiss];
            }
        } else {
            [sheet dismiss];
        }
    };
    sheet.enableAutoDismiss = NO;
    [sheet show];
}

- (void)isConfirmedAndLoginout {
    [UIWindow imy_showLoadingHUDWithText:IMYString(@"同步数据中...")];
    self.logoutBeginTime = IMYDateTimeIntervalSince1970();
    [self exitAccount];
//    @weakify(self);
//    [[IMYRecordBabyDataAutoUploadManager sharedManager] uploadToCloud:^(BOOL success, NSError *error) {
//        imy_asyncMainBlock(^{
//            @strongify(self);
//            [[IMYRecordBabyDataAutoUploadManager sharedManager] stopTimeAutoUploadData];
//            [self exitAccount];
//        });
//    }];
}

- (void)exitAccount {
    //停止自动同步
    [SYAutoUploadData sharedAutoUpload].isAutoSyncmlDo = YES;
    
    self.syncmlRecordUpload.delegate = nil;
    [self.syncmlRecordUpload cancel];
    
    self.syncmlRecordUpload = [IMYDayRecordUpload shareDayRecordUpload];
    
    self.syncmlRecordUpload.delegate = self;
    self.syncmlRecordUpload.isTempToken = NO;
    
    [[IMYDayRecordUpload shareDayRecordUpload] simpleUploadToCloud];
    
    //清空消息
    [IMYStatus dismiss];
    [[UIApplication sharedApplication] cancelAllLocalNotifications];
    [UIApplication sharedApplication].applicationIconBadgeNumber = 0;
    
    // 发送通知，取消图片上传
    IMY_POST_NOTIFY(@"BBJNotification_logout");
}

#pragma mark - #import DayRecordUpload Delegate
- (void)dayRecordFinished {
    //同步成功更新时间戳
    [SYUserHelper sharedHelper].syncmlSuccessDate = [NSDate date];
    [[SYUserHelper sharedHelper] saveToDB];
    [SYAutoUploadData sharedAutoUpload].isAutoSyncmlDo = YES;
    //同步个人信息
    [SYPublicFun uploadUserInfoData];
    
    [self.syncmlRecordUpload removeDelegate:self];
    self.syncmlRecordUpload = nil;
    
    if (![IMYPublicAppHelper shareAppHelper].hasLogin) {
        return;
    }
    
    [[[IMYAccountAuthService logout] deliverOnMainThread] subscribeNext:^(NSDictionary *dict) {
        [SYPublicFun logOutSetting];
        
        NSString *uid = dict[@"user_id"];
        uid = [NSString stringWithFormat:@"%@", uid];
        NSString *token = dict[@"authentication_token"];
        [SYPublicFun setVirtualToken:token];
        [SYPublicFun setVirtualID:uid];
        //切换数据库
        [SYUserHelper setUserID:uid];
        [SYUserHelper sendUserIDChangedEvent];
        
        const NSTimeInterval timeDiff = IMYDateTimeIntervalSince1970() - self.logoutBeginTime;
        imy_asyncMainBlock(MAX(3 - timeDiff, 0), ^{
            [UIWindow imy_showTextHUDWithDelay:0 image:[UIImage imy_imageForKey:@"toast_icon_yes"] text:IMYString(@"已退出")];
        });
        IMY_POST_NOTIFY(ChangedUserModeNotification);
        NSDictionary *params = @{@"mode": @(0),
                                 @"position": @(7)};
        [IMYGAEventHelper postWithPath:@"bi_mode" params:params headers:nil completed:nil];
        !self.loginoutBlock ?: self.loginoutBlock(YES, nil);
    } error:^(NSError *error) {
        !self.loginoutBlock ?: self.loginoutBlock(NO, error);
        [UIWindow imy_showTextHUD:IMYString(@"退出账号失败")];
    }];
}

- (void)dayRecordError:(NSString *)error {
    !self.loginoutBlock ?: self.loginoutBlock(false, error);
    [UIWindow imy_hideHUD];
    //开始自动同步
    [SYAutoUploadData sharedAutoUpload].isAutoSyncmlDo = NO;
    
    [self.syncmlRecordUpload removeDelegate:self];
    self.syncmlRecordUpload = nil;
    
    [UIWindow imy_showTextHUD:error];
}


#pragma mark - M80

- (void)m80AttributedLabel:(IMYRM80AttributedLabel *)label
             clickedOnLink:(IMYRM80AttributedLabelURL *)linkURL {
    NSString *infolistString = @"《个人信息收集清单》";
    NSString *thirdshareString = @"《第三方信息共享清单》";
    NSString *content3 = @"《隐私政策》";
    
    if ([linkURL.linkData isEqualToString:infolistString]) {
        [[IMYURIManager shareURIManager] runActionWithPath:@"web" params:@{@"url":@"https://nodejs-user.seeyouyima.com/users/info-personal.html"} info:nil];
    } else if ([linkURL.linkData isEqualToString:thirdshareString]) {
        [[IMYURIManager shareURIManager] runActionWithPath:@"web" params:@{@"url":@"https://nodejs-user.seeyouyima.com/users/info-3rd.html"} info:nil];
    } else if ([linkURL.linkData isEqualToString:content3]) {
        // 最新隐私政策协议
        [SYPublicFun onNewPrivacyPolicyDidClick];
        [[NSNotificationCenter defaultCenter] postNotificationName:@"SYBaseTabBarFB" object:nil];
        [self imy_push:[IMYVKWebViewController webWithURLString:[IMYPublicAppHelper shareAppHelper].privacyRemoteURLString]];
    }
}

#pragma mark - ShareApp
- (void)showShareApplictionSheet {
    
    IMYCoolShareConfig *config = [IMYCoolShareConfig oneLineConfig];
    config.showCollection = NO;
    
    //TODO:分享成功后埋点处理。
    [IMYCoolShareSheet customShareInViewController:self.navigationController
                                        configList:@[[config createItems]]
                                        indexBlock:^(IMYCoolShareSheetType itemType, NSInteger shareType) {
        [IMYEventHelper event:@"fxyy-qdfx"];
        NSString *fromURL = XXURL(@"view.seeyouyima.com/download");
        NSString *shareTitle = IMYString(@"我有一个超好用的APP分享给你哦~");
        NSString *shareContent = IMYString(@"女生都在用的是健康APP！帮你记录预测大姨妈，备孕怀孕育儿全程保驾护航，和美柚一起遇见更美的你~");
        [IMYPublicShareManager new].title(shareTitle).content(shareContent).imageURL(WebURL_ShareIcon).fromURL(fromURL).shareType(shareType).fromModule(107).callback(^(BOOL success){
            
        }).share();
    }];
}

@end
