//
//  IOCAppMainTabVC.h
//  Pods
//
//  Created by ljh on 2024/3/26.
//

#import <UIKit/UIKit.h>

#if __has_include(<IMYFoundation/IMYHiveMind.h>)
#import <IMYFoundation/IMYHiveMind.h>
#endif

#if __has_include(<IMYBaseKit/IMYHiveMind.h>)
#import <IMYBaseKit/IMYHiveMind.h>
#endif

NS_ASSUME_NONNULL_BEGIN

/// 该类型仅作 Tab Index 索引使用，目前只在美柚App生效
typedef NS_ENUM(NSInteger, SYTabBarIndexType) {
    /// 未知
    SYTabBarIndexTypeUnknown = -1,
    /// 美柚、资讯
    SYTabBarIndexTypeHome,
    /// 记录、孕期记录
    SYTabBarIndexTypeRecord,
    /// 她她圈、视频、新社区、返现、会员
    SYTabBarIndexTypeCircle,
    /// 柚子街、消息
    SYTabBarIndexTypeSale,
    /// 我
    SYTabBarIndexTypeMine,
    
    /// 任意Tab
    SYTabBarIndexTypeAnyXXX = 1000,
};

/**
    App主框架VC，可通过 IMYHIVE_BINDER(IOCAppMainTabVC) 获取
    如果通过 window.rootViewController 获取，需要判断是否主VC conformsToProtocol:@protocol(IOCAppMainTabVC)
 */
@protocol IOCAppMainTabVC

/// 获取和设置当前tab显示位置， 避免直接调用 selectedIndex
@property (nonatomic, assign) SYTabBarIndexType selectedTabIndexType;

/// 返回对应根节点VC，不要通过 viewControllers 直接读取
- (__kindof UINavigationController *)getRootVCWithTabIndexType:(SYTabBarIndexType)tabIndexType;

/// 页面是否显示过
@property (nonatomic, assign, readonly) BOOL isViewDidAppear;

/// 显示 tab item 角标，设置为 0 隐藏，其他数值可以看 IMYBadgeView.h 头文件
- (void)showBadgesCount:(NSInteger)count atIndexType:(SYTabBarIndexType)tabIndexType;

/// 获取对应 tab item 的角标数值
- (NSInteger)badgesCountAtIndexType:(SYTabBarIndexType)tabIndexType;

/// tab被选中通知（重复点击也会发送）
- (NSString *)getTabIndexDidSelectedNotification;

@end

NS_ASSUME_NONNULL_END
